## htsjdk.samtools.metrics.StringHeader
# MarkDuplicates --INPUT /mydata/test/lcmrd/LC2509641B01/bam/LC2509641B01_umi_aligned_sorted.bam --OUTPUT /mydata/test/lcmrd/LC2509641B01/bam/LC2509641B01_sorted_rmdups_gatk.bam --METRICS_FILE /mydata/test/lcmrd/LC2509641B01/QC/LC2509641B01_markduplicates_metrics_gatk.txt --REMOVE_DUPLICATES true --ASSUME_SORTED true --OPTICAL_DUPLICATE_PIXEL_DISTANCE 2500 --VALIDATION_STRINGENCY LENIENT --CREATE_INDEX true --MAX_SEQUENCES_FOR_DISK_READ_ENDS_MAP 50000 --MAX_FILE_HANDLES_FOR_READ_ENDS_MAP 8000 --SORTING_COLLECTION_SIZE_RATIO 0.25 --TAG_DUPLICATE_SET_MEMBERS false --REMOVE_SEQUENCING_DUPLICATES false --TAGGING_POLICY DontTag --CLEAR_DT true --DUPLEX_UMI false --ADD_PG_TAG_TO_READS true --DUPLICATE_SCORING_STRATEGY SUM_OF_BASE_QUALITIES --PROGRAM_RECORD_ID MarkDuplicates --PROGRAM_GROUP_NAME MarkDuplicates --READ_NAME_REGEX <optimized capture of last three ':' separated fields as numeric values> --MAX_OPTICAL_DUPLICATE_SET_SIZE 300000 --VERBOSITY INFO --QUIET false --COMPRESSION_LEVEL 2 --MAX_RECORDS_IN_RAM 500000 --CREATE_MD5_FILE false --GA4GH_CLIENT_SECRETS client_secrets.json --help false --version false --showHidden false --USE_JDK_DEFLATER false --USE_JDK_INFLATER false
## htsjdk.samtools.metrics.StringHeader
# Started on: Fri Jul 11 14:13:00 CST 2025

## METRICS CLASS	picard.sam.DuplicationMetrics
LIBRARY	UNPAIRED_READS_EXAMINED	READ_PAIRS_EXAMINED	SECONDARY_OR_SUPPLEMENTARY_RDS	UNMAPPED_READS	UNPAIRED_READ_DUPLICATES	READ_PAIR_DUPLICATES	READ_PAIR_OPTICAL_DUPLICATES	PERCENT_DUPLICATION	ESTIMATED_LIBRARY_SIZE
LC2509641B01	0	11219526	17105	0	0	2774234	509947	0.247268	21614763

## HISTOGRAM	java.lang.Double
BIN	CoverageMult	all_sets	optical_sets	non_optical_sets
1.0	1.036359	6349035	0	6667350
2.0	1.653071	1584874	439793	1398963
3.0	2.02006	385951	30684	295722
4.0	2.238447	94598	2556	64766
5.0	2.368403	23093	249	14214
6.0	2.445737	5802	22	3265
7.0	2.491756	1459	2	772
8.0	2.519141	354	0	173
9.0	2.535437	99	0	54
10.0	2.545135	18	0	9
11.0	2.550906	7	0	2
12.0	2.55434	0	0	1
13.0	2.556383	1	0	0
14.0	2.557599	0	0	0
15.0	2.558323	1	0	1
16.0	2.558753	0	0	0
17.0	2.55901	0	0	0
18.0	2.559162	0	0	0
19.0	2.559253	0	0	0
20.0	2.559307	0	0	0
21.0	2.559339	0	0	0
22.0	2.559358	0	0	0
23.0	2.559369	0	0	0
24.0	2.559376	0	0	0
25.0	2.55938	0	0	0
26.0	2.559383	0	0	0
27.0	2.559384	0	0	0
28.0	2.559385	0	0	0
29.0	2.559385	0	0	0
30.0	2.559386	0	0	0
31.0	2.559386	0	0	0
32.0	2.559386	0	0	0
33.0	2.559386	0	0	0
34.0	2.559386	0	0	0
35.0	2.559386	0	0	0
36.0	2.559386	0	0	0
37.0	2.559386	0	0	0
38.0	2.559386	0	0	0
39.0	2.559386	0	0	0
40.0	2.559386	0	0	0
41.0	2.559386	0	0	0
42.0	2.559386	0	0	0
43.0	2.559386	0	0	0
44.0	2.559386	0	0	0
45.0	2.559386	0	0	0
46.0	2.559386	0	0	0
47.0	2.559386	0	0	0
48.0	2.559386	0	0	0
49.0	2.559386	0	0	0
50.0	2.559386	0	0	0
51.0	2.559386	0	0	0
52.0	2.559386	0	0	0
53.0	2.559386	0	0	0
54.0	2.559386	0	0	0
55.0	2.559386	0	0	0
56.0	2.559386	0	0	0
57.0	2.559386	0	0	0
58.0	2.559386	0	0	0
59.0	2.559386	0	0	0
60.0	2.559386	0	0	0
61.0	2.559386	0	0	0
62.0	2.559386	0	0	0
63.0	2.559386	0	0	0
64.0	2.559386	0	0	0
65.0	2.559386	0	0	0
66.0	2.559386	0	0	0
67.0	2.559386	0	0	0
68.0	2.559386	0	0	0
69.0	2.559386	0	0	0
70.0	2.559386	0	0	0
71.0	2.559386	0	0	0
72.0	2.559386	0	0	0
73.0	2.559386	0	0	0
74.0	2.559386	0	0	0
75.0	2.559386	0	0	0
76.0	2.559386	0	0	0
77.0	2.559386	0	0	0
78.0	2.559386	0	0	0
79.0	2.559386	0	0	0
80.0	2.559386	0	0	0
81.0	2.559386	0	0	0
82.0	2.559386	0	0	0
83.0	2.559386	0	0	0
84.0	2.559386	0	0	0
85.0	2.559386	0	0	0
86.0	2.559386	0	0	0
87.0	2.559386	0	0	0
88.0	2.559386	0	0	0
89.0	2.559386	0	0	0
90.0	2.559386	0	0	0
91.0	2.559386	0	0	0
92.0	2.559386	0	0	0
93.0	2.559386	0	0	0
94.0	2.559386	0	0	0
95.0	2.559386	0	0	0
96.0	2.559386	0	0	0
97.0	2.559386	0	0	0
98.0	2.559386	0	0	0
99.0	2.559386	0	0	0
100.0	2.559386	0	0	0

