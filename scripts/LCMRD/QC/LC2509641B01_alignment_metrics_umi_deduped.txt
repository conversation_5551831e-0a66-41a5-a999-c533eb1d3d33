## htsjdk.samtools.metrics.StringHeader
# CollectAlignmentSummaryMetrics --METRIC_ACCUMULATION_LEVEL ALL_READS --INPUT /mydata/test/lcmrd/LC2509641B01/bam/LC2509641B01_umi_deduped_sorted.bam --OUTPUT /mydata/test/lcmrd/LC2509641B01/QC/LC2509641B01_alignment_metrics_umi_deduped.txt --VALIDATION_STRINGENCY LENIENT --REFERENCE_SEQUENCE /myTools/kbase/hg19_UCSC/hg19.fa --MAX_INSERT_SIZE 100000 --EXPECTED_PAIR_ORIENTATIONS FR --ADAPTER_SEQUENCE AATGATACGGCGACCACCGAGATCTACACTCTTTCCCTACACGACGCTCTTCCGATCT --ADAPTER_SEQUENCE AGATCGGAAGAGCTCGTATGCCGTCTTCTGCTTG --ADAPTER_SEQUENCE AATGATACGGCGACCACCGAGATCTACACTCTTTCCCTACACGACGCTCTTCCGATCT --ADAPTER_SEQUENCE AGATCGGAAGAGCGGTTCAGCAGGAATGCCGAGACCGATCTCGTATGCCGTCTTCTGCTTG --ADAPTER_SEQUENCE AATGATACGGCGACCACCGAGATCTACACTCTTTCCCTACACGACGCTCTTCCGATCT --ADAPTER_SEQUENCE AGATCGGAAGAGCACACGTCTGAACTCCAGTCACNNNNNNNNATCTCGTATGCCGTCTTCTGCTTG --IS_BISULFITE_SEQUENCED false --COLLECT_ALIGNMENT_INFORMATION true --ASSUME_SORTED true --STOP_AFTER 0 --VERBOSITY INFO --QUIET false --COMPRESSION_LEVEL 2 --MAX_RECORDS_IN_RAM 500000 --CREATE_INDEX false --CREATE_MD5_FILE false --GA4GH_CLIENT_SECRETS client_secrets.json --help false --version false --showHidden false --USE_JDK_DEFLATER false --USE_JDK_INFLATER false
## htsjdk.samtools.metrics.StringHeader
# Started on: Thu Jul 03 15:29:51 CST 2025

## METRICS CLASS	picard.analysis.AlignmentSummaryMetrics
CATEGORY	TOTAL_READS	PF_READS	PCT_PF_READS	PF_NOISE_READS	PF_READS_ALIGNED	PCT_PF_READS_ALIGNED	PF_ALIGNED_BASES	PF_HQ_ALIGNED_READS	PF_HQ_ALIGNED_BASES	PF_HQ_ALIGNED_Q20_BASES	PF_HQ_MEDIAN_MISMATCHES	PF_MISMATCH_RATE	PF_HQ_ERROR_RATE	PF_INDEL_RATE	MEAN_READ_LENGTH	READS_ALIGNED_IN_PAIRS	PCT_READS_ALIGNED_IN_PAIRS	PF_READS_IMPROPER_PAIRS	PCT_PF_READS_IMPROPER_PAIRS	BAD_CYCLES	STRAND_BALANCE	PCT_CHIMERAS	PCT_ADAPTER	PCT_SOFTCLIP	PCT_HARDCLIP	AVG_POS_3PRIME_SOFTCLIP_LENGTH	SAMPLE	LIBRARY	READ_GROUP
FIRST_OF_PAIR	8380056	8380056	1	0	8379809	0.999971	1167669254	8335420	1161677482	1159772315	0	0.003388	0.003301	0.000135	139.468163	8379807	1	877	0.000105	0	0.499875	0.0005	0	0.000736	0	15.213151			
SECOND_OF_PAIR	8380056	8380056	1	0	8379809	0.999971	1167594070	8335561	1161628760	1159269427	0	0.003795	0.003705	0.000135	139.467084	8379807	1	877	0.000105	0	0.500125	0.000545	0	0.000794	0	15.596068			
PAIR	16760112	16760112	1	0	16759618	0.999971	2335263324	16670981	2323306242	2319041742	0	0.003592	0.003503	0.000135	139.467624	16759614	1	1754	0.000105	0	0.5	0.000522	0	0.000765	0	15.407956			

## HISTOGRAM	java.lang.Integer
READ_LENGTH	PAIRED_TOTAL_LENGTH_COUNT	PAIRED_ALIGNED_LENGTH_COUNT
0	0	494
2	18	0
3	24	0
4	6	0
5	16	0
6	18	0
7	8	0
8	16	0
9	11	0
10	18	0
11	26	0
12	15	0
13	14	0
14	19	0
15	26	0
16	18	0
17	9	0
18	10	0
19	15	35
20	27	56
21	31	39
22	25	27
23	24	32
24	33	39
25	30	31
26	25	23
27	41	45
28	32	35
29	66	69
30	77	69
31	6	23
32	4	27
33	8	20
34	9	26
35	13	32
36	6	18
37	4	22
38	6	17
39	11	21
40	13	25
41	8	26
42	19	45
43	11	34
44	11	34
45	11	25
46	12	41
47	13	64
48	9	45
49	19	59
50	10	51
51	8	66
52	17	63
53	10	75
54	11	84
55	11	84
56	3	76
57	11	92
58	8	124
59	10	108
60	8	119
61	7	133
62	11	148
63	11	137
64	15	152
65	19	162
66	9	189
67	27	204
68	15	209
69	15	227
70	24	266
71	28	281
72	25	315
73	28	294
74	40	375
75	12979	13175
76	13035	13244
77	13688	13978
78	14331	14590
79	15056	15357
80	16016	16281
81	16381	16677
82	17247	17561
83	18180	18503
84	18755	19137
85	19820	20087
86	20483	20813
87	21483	21850
88	22439	22852
89	22590	22969
90	24098	24517
91	25010	25373
92	25744	26132
93	26383	26807
94	27359	27728
95	27925	28317
96	29314	29737
97	30257	30763
98	30949	31451
99	31784	32280
100	33329	33760
101	34190	34759
102	34486	35061
103	35790	37355
104	36331	36903
105	38000	38359
106	38345	38832
107	39992	40456
108	40964	41420
109	41647	42197
110	43135	43676
111	43872	44362
112	44210	44848
113	45371	45986
114	46218	46886
115	47549	48313
116	48292	49003
117	48901	49548
118	50447	51190
119	51115	52008
120	52097	52983
121	53171	54091
122	54150	55216
123	54739	55722
124	55543	56641
125	56309	57568
126	57132	58393
127	57176	58693
128	59229	60695
129	60285	62057
130	60495	62175
131	61541	63502
132	61942	63914
133	62976	64926
134	63892	66767
135	64480	67441
136	64869	67615
137	66233	69164
138	66685	68947
139	68259	73958
140	68988	73898
141	70584	75564
142	74736	78922
143	100510	98209
144	13859329	13782318

