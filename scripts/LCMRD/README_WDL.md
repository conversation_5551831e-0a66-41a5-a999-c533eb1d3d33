# QC Summary WDL Workflow

This WDL workflow converts the original Perl script `QC_summary_20250610.pl` into a multi-step workflow for generating quality control summary reports from sequencing data.

## Overview

The workflow consists of 8 tasks that process various QC metrics files and generate three output files:

1. **`${sample_id}_qc_summary.txt`** - Detailed QC metrics in key-value format
2. **`${sample_id}_qc_summary.tsv`** - QC metrics in tab-separated format
3. **`${sample_id}_qc.csv`** - Chinese language QC report in CSV format

## Workflow Tasks

### 1. ParseFastpLog
- **Input**: FastP log file
- **Function**: Extracts read statistics, quality scores, and data sizes
- **Outputs**: Total reads, raw/clean data sizes, Q20/Q30 percentages

### 2. ParseAlignmentMetrics
- **Input**: Alignment metrics files (before and after UMI deduplication)
- **Function**: Calculates alignment rates and duplication rates
- **Outputs**: Alignment statistics and UMI duplicate rate

### 3. ParseOnTargetReads
- **Input**: On-target reads files and alignment statistics
- **Function**: Calculates on-target rates
- **Outputs**: On-target percentages before and after UMI deduplication

### 4. ParseHsMetrics
- **Input**: Hybrid selection metrics files
- **Function**: Extracts coverage metrics and target region statistics
- **Outputs**: Coverage depths, target coverage percentages, fold penalties

### 5. ParsePerBaseCoverage
- **Input**: Per-base coverage file and median coverage
- **Function**: Analyzes coverage distribution across target regions
- **Outputs**: Coverage uniformity statistics

### 6. ParseInsertSizeMetrics
- **Input**: Insert size metrics file
- **Function**: Extracts insert size statistics
- **Outputs**: Median and mean insert sizes

### 7. ParseDnaQc
- **Input**: DNA QC file and sample ID
- **Function**: Extracts DNA mass information
- **Outputs**: DNA mass value

### 8. GenerateQcSummary
- **Input**: All metrics from previous tasks
- **Function**: Generates the three output files
- **Outputs**: QC summary files in different formats

## Required Input Files

The workflow requires the following input files for each sample:

```
fastq/${sample}_fastp.log                           # FastP filtering results
QC/${sample}_alignment_metrics.txt                  # Alignment metrics
QC/${sample}_alignment_metrics_umi_deduped.txt      # UMI-deduplicated alignment metrics
QC/${sample}_on_target_reads.txt                    # On-target reads count
QC/${sample}_on_target_reads_umi_deduped.txt        # UMI-deduplicated on-target reads
QC/${sample}_hs_metrics_sorted.txt                  # Hybrid selection metrics
QC/${sample}_hs_metrics_umi_deduped.txt             # UMI-deduplicated hybrid selection metrics
QC/${sample}_per_base_coverage_umi_deduped.txt      # Per-base coverage data
QC/${sample}_insert_size_metrics_umi_deduped.txt    # Insert size metrics
dna_qc.txt                                          # DNA quality control data
```

## Usage

### 1. Using Cromwell

```bash
# Run the workflow with Cromwell
java -jar cromwell.jar run qc_summary_workflow.wdl -i qc_summary_inputs.json
```

### 2. Using miniwdl

```bash
# Validate the workflow
miniwdl check qc_summary_workflow.wdl

# Run the workflow
miniwdl run qc_summary_workflow.wdl -i qc_summary_inputs.json
```

### 3. Input Configuration

Edit the `qc_summary_inputs.json` file to specify your sample ID and file paths:

```json
{
  "QcSummaryWorkflow.sample_id": "YOUR_SAMPLE_ID",
  "QcSummaryWorkflow.work_dir": "/path/to/your/work/directory/",
  "QcSummaryWorkflow.fastp_log": "/path/to/fastq/YOUR_SAMPLE_ID_fastp.log",
  ...
}
```

## Output Files

The workflow generates three output files:

1. **Text format** (`${sample_id}_qc_summary.txt`):
   ```
   Sample_ID:LC2509641B01
   Raw_data_size:12345678
   Total_reads(M pairs):5.67
   Q20_R1(%):95.23
   ...
   ```

2. **TSV format** (`${sample_id}_qc_summary.tsv`):
   Tab-separated values suitable for spreadsheet analysis

3. **CSV format** (`${sample_id}_qc.csv`):
   Chinese language report for clinical use

## Dependencies

- **Docker**: The workflow uses lightweight Ubuntu 20.04 Docker containers
- **Shell Tools**: Standard Unix tools (awk, grep, cut, bc) - available in Ubuntu base image
- **WDL Runtime**: Cromwell, miniwdl, or other WDL-compatible execution engine

## Advantages of WDL Version

1. **Modularity**: Each processing step is a separate task
2. **Parallelization**: Independent tasks can run in parallel
3. **Reproducibility**: Containerized execution ensures consistent results
4. **Scalability**: Can process multiple samples simultaneously
5. **Error Handling**: Better error isolation and debugging
6. **Resource Management**: Explicit memory and CPU requirements
7. **Portability**: Runs on various execution platforms (local, cloud, HPC)
8. **Lightweight**: Uses shell commands instead of Python, reducing dependencies
9. **Efficiency**: Smaller Docker images and lower memory requirements
10. **Performance**: Native shell tools are often faster than interpreted languages

## Troubleshooting

- Ensure all input files exist and are readable
- Check that file paths in the input JSON are absolute paths
- Verify Docker is available if running locally
- Check log files for specific error messages from individual tasks
