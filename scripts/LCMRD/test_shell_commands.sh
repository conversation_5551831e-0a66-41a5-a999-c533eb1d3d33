#!/bin/bash

# Test script to validate shell commands used in WDL tasks
# This script tests the key shell operations without running the full WDL workflow

echo "Testing shell commands used in WDL workflow..."

# Test 1: bc arithmetic operations
echo "Test 1: Testing bc arithmetic operations..."
result1=$(echo "scale=2; 1234567 / 1000000" | bc -l)
echo "  Total reads calculation: $result1 (expected: 1.23)"

result2=$(echo "scale=2; 0.95 * 100" | bc -l)
echo "  Percentage calculation: $result2 (expected: 95.00)"

result3=$(echo "scale=2; (1000 - 800) * 100 / 1000" | bc -l)
echo "  Duplicate rate calculation: $result3 (expected: 20.00)"

# Test 2: awk field extraction and formatting
echo -e "\nTest 2: Testing awk operations..."
echo -e "PAIR\t100\t200\t300\t400\t500\t0.95\t700" | awk '{printf "%.2f", $7 * 100}'
echo "  (expected: 95.00)"

# Test 3: grep pattern matching
echo -e "\nTest 3: Testing grep operations..."
echo -e "Read1 before filtering:\ntotal reads: 1234567\ntotal bases: 123456789" | grep -A2 "Read1 before filtering:" | grep "total reads:" | awk '{print $3}'
echo "  (expected: 1234567)"

# Test 4: sed pattern extraction
echo -e "\nTest 4: Testing sed operations..."
echo "Q20 bases: 12345(95.67%)" | sed 's/.*(\([0-9.]*\)%).*/\1/'
echo "  (expected: 95.67)"

# Test 5: String manipulation for sample type
echo -e "\nTest 5: Testing sample type detection..."
sample1="LC2509641B01"
sample2="LC2509641C01"

if [[ "${sample1:9:1}" == "B" ]]; then
    echo "  $sample1 -> gDNA样本 (correct)"
else
    echo "  $sample1 -> ctDNA样本"
fi

if [[ "${sample2:9:1}" == "B" ]]; then
    echo "  $sample2 -> gDNA样本"
else
    echo "  $sample2 -> ctDNA样本 (correct)"
fi

# Test 6: Coverage threshold calculations
echo -e "\nTest 6: Testing coverage calculations..."
median_cov=1000.5
fold2_min=$(echo "scale=6; $median_cov / 2" | bc -l)
fold2_max=$(echo "scale=6; $median_cov * 2" | bc -l)
point1_threshold=$(echo "scale=6; $median_cov * 0.1" | bc -l)

echo "  Median coverage: $median_cov"
echo "  2-fold range: $fold2_min - $fold2_max"
echo "  0.1x threshold: $point1_threshold"

# Test 7: File processing simulation
echo -e "\nTest 7: Testing file processing..."
# Create a temporary test file
cat > test_coverage.txt << EOF
chrom	start	end	coverage
chr1	1	2	500.25
chr1	2	3	1500.75
chr1	3	4	2000.00
chr1	4	5	100.10
EOF

# Count bases in 2-fold range
fold2_count=$(awk -v min="$fold2_min" -v max="$fold2_max" 'NR>1 && $4 >= min && $4 <= max {count++} END {print count+0}' test_coverage.txt)
echo "  Bases in 2-fold range: $fold2_count (expected: 2)"

# Clean up
rm -f test_coverage.txt

echo -e "\nAll shell command tests completed!"
echo "If all results match expected values, the shell commands are working correctly."
