version 1.0

workflow QcSummaryWorkflow {
    input {
        String sample_id
        String work_dir
        File fastp_log
        File alignment_metrics
        File alignment_metrics_umi_deduped
        File on_target_reads
        File on_target_reads_umi_deduped
        File hs_metrics_sorted
        File hs_metrics_umi_deduped
        File per_base_coverage_umi_deduped
        File insert_size_metrics_umi_deduped
        File dna_qc
    }

    call ParseFastpLog {
        input:
            fastp_log = fastp_log
    }

    call ParseAlignmentMetrics {
        input:
            alignment_metrics = alignment_metrics,
            alignment_metrics_umi_deduped = alignment_metrics_umi_deduped
    }

    call ParseOnTargetReads {
        input:
            on_target_reads = on_target_reads,
            on_target_reads_umi_deduped = on_target_reads_umi_deduped,
            pf_reads_aligned = ParseAlignmentMetrics.pf_reads_aligned,
            pf_reads_aligned_umi_deduped = ParseAlignmentMetrics.pf_reads_aligned_umi_deduped
    }

    call ParseHsMetrics {
        input:
            hs_metrics_sorted = hs_metrics_sorted,
            hs_metrics_umi_deduped = hs_metrics_umi_deduped
    }

    call ParsePerBaseCoverage {
        input:
            per_base_coverage = per_base_coverage_umi_deduped,
            median_coverage = ParseHsMetrics.median_coverage_after_umi_dedup,
            target_territory = ParseHsMetrics.target_territory
    }

    call ParseInsertSizeMetrics {
        input:
            insert_size_metrics = insert_size_metrics_umi_deduped
    }

    call ParseDnaQc {
        input:
            dna_qc = dna_qc,
            sample_id = sample_id
    }

    call GenerateQcSummary {
        input:
            sample_id = sample_id,
            work_dir = work_dir,
            # FastP metrics
            total_reads_pair = ParseFastpLog.total_reads_pair,
            raw_data_size = ParseFastpLog.raw_data_size,
            clean_data_size = ParseFastpLog.clean_data_size,
            read1_q20_after_filtering = ParseFastpLog.read1_q20_after_filtering,
            read2_q20_after_filtering = ParseFastpLog.read2_q20_after_filtering,
            read1_q30_after_filtering = ParseFastpLog.read1_q30_after_filtering,
            read2_q30_after_filtering = ParseFastpLog.read2_q30_after_filtering,
            # Alignment metrics
            pct_pf_reads_aligned = ParseAlignmentMetrics.pct_pf_reads_aligned,
            umi_total_duplicate_rate = ParseAlignmentMetrics.umi_total_duplicate_rate,
            # On-target metrics
            reads_on_target_rate = ParseOnTargetReads.reads_on_target_rate,
            reads_on_target_rate_after_umi_dedup = ParseOnTargetReads.reads_on_target_rate_after_umi_dedup,
            # HS metrics
            mean_coverage = ParseHsMetrics.mean_coverage,
            median_coverage = ParseHsMetrics.median_coverage,
            mean_coverage_after_umi_dedup = ParseHsMetrics.mean_coverage_after_umi_dedup,
            median_coverage_after_umi_dedup = ParseHsMetrics.median_coverage_after_umi_dedup,
            fold80_base_penalty = ParseHsMetrics.fold80_base_penalty,
            coverage_of_target_region_1X = ParseHsMetrics.coverage_of_target_region_1X,
            coverage_of_target_region_1000X = ParseHsMetrics.coverage_of_target_region_1000X,
            coverage_of_target_region_2500X = ParseHsMetrics.coverage_of_target_region_2500X,
            coverage_of_target_region_5000X = ParseHsMetrics.coverage_of_target_region_5000X,
            # Coverage statistics
            percent_bases_in_2_fold_range = ParsePerBaseCoverage.percent_bases_in_2_fold_range,
            percent_bases_point1_fold_of_unique_depth = ParsePerBaseCoverage.percent_bases_point1_fold_of_unique_depth,
            percent_bases_point2_fold_of_unique_depth = ParsePerBaseCoverage.percent_bases_point2_fold_of_unique_depth,
            percent_bases_point5_fold_of_unique_depth = ParsePerBaseCoverage.percent_bases_point5_fold_of_unique_depth,
            # Insert size metrics
            median_insert_size = ParseInsertSizeMetrics.median_insert_size,
            mean_insert_size = ParseInsertSizeMetrics.mean_insert_size,
            # DNA QC
            mass = ParseDnaQc.mass
    }

    output {
        File qc_summary_txt = GenerateQcSummary.qc_summary_txt
        File qc_summary_tsv = GenerateQcSummary.qc_summary_tsv
        File qc_csv = GenerateQcSummary.qc_csv
    }
}

task ParseFastpLog {
    input {
        File fastp_log
    }

    command <<<
        # Extract Read1 before filtering total reads and bases
        read1_reads=$(grep -A2 "Read1 before filtering:" ~{fastp_log} | grep "total reads:" | awk '{print $3}')
        read1_bases_before=$(grep -A2 "Read1 before filtering:" ~{fastp_log} | grep "total bases:" | awk '{print $3}')

        # Extract Read2 before filtering bases
        read2_bases_before=$(grep -A2 "Read2 before filtering:" ~{fastp_log} | grep "total bases:" | awk '{print $3}')

        # Extract Read1 after filtering metrics
        read1_bases_after=$(grep -A5 "Read1 after filtering:" ~{fastp_log} | grep "total bases:" | awk '{print $3}')
        read1_q20=$(grep -A5 "Read1 after filtering:" ~{fastp_log} | grep "Q20 bases:" | sed 's/.*(\([0-9.]*\)%).*/\1/')
        read1_q30=$(grep -A5 "Read1 after filtering:" ~{fastp_log} | grep "Q30 bases:" | sed 's/.*(\([0-9.]*\)%).*/\1/')

        # Extract Read2 after filtering metrics
        read2_bases_after=$(grep -A5 "Read2 after filtering:" ~{fastp_log} | grep "total bases:" | awk '{print $3}')
        read2_q20=$(grep -A5 "Read2 after filtering:" ~{fastp_log} | grep "Q20 bases:" | sed 's/.*(\([0-9.]*\)%).*/\1/')
        read2_q30=$(grep -A5 "Read2 after filtering:" ~{fastp_log} | grep "Q30 bases:" | sed 's/.*(\([0-9.]*\)%).*/\1/')

        # Calculate derived values
        total_reads_pair=$(echo "scale=2; $read1_reads / 1000000" | bc -l)
        raw_data_size=$(echo "$read1_bases_before + $read2_bases_before" | bc)
        clean_data_size=$(echo "$read1_bases_after + $read2_bases_after" | bc)

        # Output results in order (stdout will be captured)
        echo "$total_reads_pair"
        echo "$raw_data_size"
        echo "$clean_data_size"
        echo "$read1_q20"
        echo "$read2_q20"
        echo "$read1_q30"
        echo "$read2_q30"
    >>>

    output {
        Array[String] results = read_lines(stdout())
        Float total_reads_pair = results[0]
        Int raw_data_size = results[1]
        Int clean_data_size = results[2]
        Float read1_q20_after_filtering = results[3]
        Float read2_q20_after_filtering = results[4]
        Float read1_q30_after_filtering = results[5]
        Float read2_q30_after_filtering = results[6]
    }

    runtime {
        docker: "ubuntu:20.04"
        memory: "512 MB"
        cpu: 1
    }
}

task ParseAlignmentMetrics {
    input {
        File alignment_metrics
        File alignment_metrics_umi_deduped
    }

    command <<<
        # Parse alignment metrics - get PF_READS_ALIGNED (field 6) and PCT_PF_READS_ALIGNED (field 7)
        pf_reads_aligned=$(grep "^PAIR" ~{alignment_metrics} | cut -f6)
        pct_pf_reads_aligned_raw=$(grep "^PAIR" ~{alignment_metrics} | cut -f7)
        pct_pf_reads_aligned=$(echo "scale=2; $pct_pf_reads_aligned_raw * 100" | bc -l)

        # Parse UMI deduplicated alignment metrics - get PF_READS_ALIGNED (field 6)
        pf_reads_aligned_umi_deduped=$(grep "^PAIR" ~{alignment_metrics_umi_deduped} | cut -f6)

        # Calculate UMI duplicate rate
        umi_total_duplicate_rate=$(echo "scale=2; ($pf_reads_aligned - $pf_reads_aligned_umi_deduped) * 100 / $pf_reads_aligned" | bc -l)

        # Output results in order
        echo "$pf_reads_aligned"
        echo "$pct_pf_reads_aligned"
        echo "$pf_reads_aligned_umi_deduped"
        echo "$umi_total_duplicate_rate"
    >>>

    output {
        Array[String] results = read_lines(stdout())
        Int pf_reads_aligned = results[0]
        Float pct_pf_reads_aligned = results[1]
        Int pf_reads_aligned_umi_deduped = results[2]
        Float umi_total_duplicate_rate = results[3]
    }

    runtime {
        docker: "ubuntu:20.04"
        memory: "256 MB"
        cpu: 1
    }
}

task ParseOnTargetReads {
    input {
        File on_target_reads
        File on_target_reads_umi_deduped
        Int pf_reads_aligned
        Int pf_reads_aligned_umi_deduped
    }

    command <<<
        # Parse on-target reads (first number in file)
        on_target_reads=$(grep -E "^[0-9]+$" ~{on_target_reads} | head -1)

        # Parse on-target reads after UMI deduplication (first number in file)
        on_target_reads_after_umi_dedup_raw=$(grep -E "^[0-9]+$" ~{on_target_reads_umi_deduped} | head -1)
        on_target_reads_after_umi_dedup=$(echo "$on_target_reads_after_umi_dedup_raw * 100" | bc)

        # Calculate rates
        reads_on_target_rate=$(echo "scale=2; $on_target_reads * 100 / ~{pf_reads_aligned}" | bc -l)
        reads_on_target_rate_after_umi_dedup=$(echo "scale=2; $on_target_reads_after_umi_dedup / ~{pf_reads_aligned_umi_deduped}" | bc -l)

        # Output results in order
        echo "$reads_on_target_rate"
        echo "$reads_on_target_rate_after_umi_dedup"
    >>>

    output {
        Array[String] results = read_lines(stdout())
        Float reads_on_target_rate = results[0]
        Float reads_on_target_rate_after_umi_dedup = results[1]
    }

    runtime {
        docker: "ubuntu:20.04"
        memory: "256 MB"
        cpu: 1
    }
}

task ParseHsMetrics {
    input {
        File hs_metrics_sorted
        File hs_metrics_umi_deduped
    }

    command <<<
        # Parse HS metrics sorted - get mean coverage (field 34) and median coverage (field 35)
        mean_coverage=$(grep "^DESIGN" ~{hs_metrics_sorted} | cut -f34 | awk '{printf "%.2f", $1}')
        median_coverage=$(grep "^DESIGN" ~{hs_metrics_sorted} | cut -f35 | awk '{printf "%.2f", $1}')

        # Parse HS metrics UMI deduplicated
        hs_line=$(grep "^DESIGN" ~{hs_metrics_umi_deduped})
        fold80_base_penalty=$(echo "$hs_line" | cut -f45 | awk '{printf "%.2f", $1}')
        mean_coverage_after_umi_dedup=$(echo "$hs_line" | cut -f34 | awk '{printf "%.2f", $1}')
        median_coverage_after_umi_dedup=$(echo "$hs_line" | cut -f35 | awk '{printf "%.2f", $1}')
        target_territory=$(echo "$hs_line" | cut -f21 | awk '{printf "%.2f", $1}')
        coverage_of_target_region_1X=$(echo "$hs_line" | cut -f46 | awk '{printf "%.2f", $1 * 100}')
        coverage_of_target_region_1000X=$(echo "$hs_line" | cut -f56 | awk '{printf "%.2f", $1 * 100}')
        coverage_of_target_region_2500X=$(echo "$hs_line" | cut -f57 | awk '{printf "%.2f", $1 * 100}')
        coverage_of_target_region_5000X=$(echo "$hs_line" | cut -f58 | awk '{printf "%.2f", $1 * 100}')

        # Output results in order
        echo "$mean_coverage"
        echo "$median_coverage"
        echo "$fold80_base_penalty"
        echo "$mean_coverage_after_umi_dedup"
        echo "$median_coverage_after_umi_dedup"
        echo "$target_territory"
        echo "$coverage_of_target_region_1X"
        echo "$coverage_of_target_region_1000X"
        echo "$coverage_of_target_region_2500X"
        echo "$coverage_of_target_region_5000X"
    >>>

    output {
        Array[String] results = read_lines(stdout())
        Float mean_coverage = results[0]
        Float median_coverage = results[1]
        Float fold80_base_penalty = results[2]
        Float mean_coverage_after_umi_dedup = results[3]
        Float median_coverage_after_umi_dedup = results[4]
        Float target_territory = results[5]
        Float coverage_of_target_region_1X = results[6]
        Float coverage_of_target_region_1000X = results[7]
        Float coverage_of_target_region_2500X = results[8]
        Float coverage_of_target_region_5000X = results[9]
    }

    runtime {
        docker: "ubuntu:20.04"
        memory: "256 MB"
        cpu: 1
    }
}

task ParsePerBaseCoverage {
    input {
        File per_base_coverage
        Float median_coverage
        Float target_territory
    }

    command <<<
        # Calculate thresholds
        median_cov=~{median_coverage}
        target_terr=~{target_territory}

        fold2_min=$(echo "scale=6; $median_cov / 2" | bc -l)
        fold2_max=$(echo "scale=6; $median_cov * 2" | bc -l)
        point1_threshold=$(echo "scale=6; $median_cov * 0.1" | bc -l)
        point2_threshold=$(echo "scale=6; $median_cov * 0.2" | bc -l)
        point5_threshold=$(echo "scale=6; $median_cov * 0.5" | bc -l)

        # Count bases meeting each criteria (skip header line)
        fold2_range_bases=$(awk -v min="$fold2_min" -v max="$fold2_max" 'NR>1 && $4 >= min && $4 <= max {count++} END {print count+0}' ~{per_base_coverage})
        point1_coverage_bases=$(awk -v thresh="$point1_threshold" 'NR>1 && $4 >= thresh {count++} END {print count+0}' ~{per_base_coverage})
        point2_coverage_bases=$(awk -v thresh="$point2_threshold" 'NR>1 && $4 >= thresh {count++} END {print count+0}' ~{per_base_coverage})
        point5_coverage_bases=$(awk -v thresh="$point5_threshold" 'NR>1 && $4 >= thresh {count++} END {print count+0}' ~{per_base_coverage})

        # Calculate percentages
        percent_bases_in_2_fold_range=$(echo "scale=2; $fold2_range_bases * 100 / $target_terr" | bc -l)
        percent_bases_point1_fold_of_unique_depth=$(echo "scale=2; $point1_coverage_bases * 100 / $target_terr" | bc -l)
        percent_bases_point2_fold_of_unique_depth=$(echo "scale=2; $point2_coverage_bases * 100 / $target_terr" | bc -l)
        percent_bases_point5_fold_of_unique_depth=$(echo "scale=2; $point5_coverage_bases * 100 / $target_terr" | bc -l)

        # Output results in order
        echo "$percent_bases_in_2_fold_range"
        echo "$percent_bases_point1_fold_of_unique_depth"
        echo "$percent_bases_point2_fold_of_unique_depth"
        echo "$percent_bases_point5_fold_of_unique_depth"
    >>>

    output {
        Array[String] results = read_lines(stdout())
        Float percent_bases_in_2_fold_range = results[0]
        Float percent_bases_point1_fold_of_unique_depth = results[1]
        Float percent_bases_point2_fold_of_unique_depth = results[2]
        Float percent_bases_point5_fold_of_unique_depth = results[3]
    }

    runtime {
        docker: "ubuntu:20.04"
        memory: "1 GB"
        cpu: 1
    }
}

task ParseInsertSizeMetrics {
    input {
        File insert_size_metrics
    }

    command <<<
        # Find the line after MEDIAN_INSERT_SIZE header and extract values
        data_line=$(grep -A1 "^MEDIAN_INSERT_SIZE" ~{insert_size_metrics} | tail -1)

        # Extract median insert size (field 1) and mean insert size (field 6)
        median_insert_size=$(echo "$data_line" | cut -f1)
        mean_insert_size=$(echo "$data_line" | cut -f6 | awk '{printf "%.2f", $1}')

        # Output results in order
        echo "$median_insert_size"
        echo "$mean_insert_size"
    >>>

    output {
        Array[String] results = read_lines(stdout())
        Int median_insert_size = results[0]
        Float mean_insert_size = results[1]
    }

    runtime {
        docker: "ubuntu:20.04"
        memory: "256 MB"
        cpu: 1
    }
}

task ParseDnaQc {
    input {
        File dna_qc
        String sample_id
    }

    command <<<
        # Find the line matching the sample ID and extract the mass (field 2)
        # Skip header line and match sample ID in first field
        mass=$(awk -v sample="~{sample_id}" 'NR>1 && $1 == sample {print $2; exit}' ~{dna_qc})

        # If no match found, set to 0.0
        if [ -z "$mass" ]; then
            mass="0.0"
        fi

        # Output result
        echo "$mass"
    >>>

    output {
        Array[String] results = read_lines(stdout())
        Float mass = results[0]
    }

    runtime {
        docker: "ubuntu:20.04"
        memory: "256 MB"
        cpu: 1
    }
}

task GenerateQcSummary {
    input {
        String sample_id
        String work_dir
        # FastP metrics
        Float total_reads_pair
        Int raw_data_size
        Int clean_data_size
        Float read1_q20_after_filtering
        Float read2_q20_after_filtering
        Float read1_q30_after_filtering
        Float read2_q30_after_filtering
        # Alignment metrics
        Float pct_pf_reads_aligned
        Float umi_total_duplicate_rate
        # On-target metrics
        Float reads_on_target_rate
        Float reads_on_target_rate_after_umi_dedup
        # HS metrics
        Float mean_coverage
        Float median_coverage
        Float mean_coverage_after_umi_dedup
        Float median_coverage_after_umi_dedup
        Float fold80_base_penalty
        Float coverage_of_target_region_1X
        Float coverage_of_target_region_1000X
        Float coverage_of_target_region_2500X
        Float coverage_of_target_region_5000X
        # Coverage statistics
        Float percent_bases_in_2_fold_range
        Float percent_bases_point1_fold_of_unique_depth
        Float percent_bases_point2_fold_of_unique_depth
        Float percent_bases_point5_fold_of_unique_depth
        # Insert size metrics
        Int median_insert_size
        Float mean_insert_size
        # DNA QC
        Float mass
    }

    command <<<
        sample_id="~{sample_id}"

        # Determine sample type based on sample ID pattern (10th character is 'B' for gDNA)
        if [[ "${sample_id:9:1}" == "B" ]]; then
            sample_type="gDNA样本"
        else
            sample_type="ctDNA样本"
        fi

        # Generate QC summary text file
        cat > "${sample_id}_qc_summary.txt" << EOF
Sample_ID:${sample_id}
Raw_data_size:~{raw_data_size}
Total_reads(M pairs):~{total_reads_pair}
Q20_R1(%):~{read1_q20_after_filtering}
Q20_R2(%):~{read2_q20_after_filtering}
Q30_R1(%):~{read1_q30_after_filtering}
Q30_R2(%):~{read2_q30_after_filtering}
Median_insert_size(bp):~{median_insert_size}
Mean_insert_size(bp):~{mean_insert_size}
Duplication_rate(%):~{umi_total_duplicate_rate}
Mapping_rate(%):~{pct_pf_reads_aligned}
On_target_rate(%):~{reads_on_target_rate}
On_target_rate_umi_dedup(%):~{reads_on_target_rate_after_umi_dedup}
Coverage_of_target_region(%):~{coverage_of_target_region_1X}
Coverage_of_target_region_1000X(%):~{coverage_of_target_region_1000X}
Coverage_of_target_region_2500X(%):~{coverage_of_target_region_2500X}
Coverage_of_target_region_5000X(%):~{coverage_of_target_region_5000X}
Mean_depth:~{mean_coverage}
Mean_depth_umi_dedup:~{mean_coverage_after_umi_dedup}
Fold_80_base_penalty:~{fold80_base_penalty}
Bases_in_2fold_range(%):~{percent_bases_in_2_fold_range}
Bases_0.1x_unique_depth(%):~{percent_bases_point1_fold_of_unique_depth}
Bases_0.2x_unique_depth(%):~{percent_bases_point2_fold_of_unique_depth}
Bases_0.5x_unique_depth(%):~{percent_bases_point5_fold_of_unique_depth}
EOF

        # Generate QC summary TSV file
        {
            echo -e "Sample_ID\tRaw_data_size\tTotal_reads(M pairs)\tQ20_R1(%)\tQ20_R2(%)\tQ30_R1(%)\tQ30_R2(%)\tMedian_insert_size(bp)\tMean_insert_size(bp)\tDuplication_rate(%)\tMapping_rate(%)\tOn_target_rate(%)\tOn_target_rate_umi_dedup(%)\tCoverage_of_target_region(%)\tCoverage_of_target_region_1000X(%)\tCoverage_of_target_region_2500X(%)\tCoverage_of_target_region_5000X(%)\tMean_depth\tMean_depth_umi_dedup\tFold_80_base_penalty\tBases_in_2fold_range(%)\tBases_0.1x_unique_depth(%)\tBases_0.2x_unique_depth(%)\tBases_0.5x_unique_depth(%)"
            echo -e "${sample_id}\t~{raw_data_size}\t~{total_reads_pair}\t~{read1_q20_after_filtering}\t~{read2_q20_after_filtering}\t~{read1_q30_after_filtering}\t~{read2_q30_after_filtering}\t~{median_insert_size}\t~{mean_insert_size}\t~{umi_total_duplicate_rate}\t~{pct_pf_reads_aligned}\t~{reads_on_target_rate}\t~{reads_on_target_rate_after_umi_dedup}\t~{coverage_of_target_region_1X}\t~{coverage_of_target_region_1000X}\t~{coverage_of_target_region_2500X}\t~{coverage_of_target_region_5000X}\t~{mean_coverage}\t~{mean_coverage_after_umi_dedup}\t~{fold80_base_penalty}\t~{percent_bases_in_2_fold_range}\t~{percent_bases_point1_fold_of_unique_depth}\t~{percent_bases_point2_fold_of_unique_depth}\t~{percent_bases_point5_fold_of_unique_depth}"
        } > "${sample_id}_qc_summary.tsv"

        # Generate QC CSV file (Chinese format)
        cat > "${sample_id}_qc.csv" << EOF
质量参数,,${sample_type}
DNA质量评估,送检DNA总量,~{mass}ng
测序质量评估,测序数据量,~{total_reads_pair}M
,插入片段长度,~{median_insert_size}bp
,序列回贴比率,~{pct_pf_reads_aligned}%
,中靶率,~{reads_on_target_rate}%
,平均测序深度,~{mean_coverage}x
,测序均一性,~{coverage_of_target_region_1000X}%
,总体质量评估,合格
EOF
    >>>

    output {
        File qc_summary_txt = "${sample_id}_qc_summary.txt"
        File qc_summary_tsv = "${sample_id}_qc_summary.tsv"
        File qc_csv = "${sample_id}_qc.csv"
    }

    runtime {
        docker: "ubuntu:20.04"
        memory: "256 MB"
        cpu: 1
    }
}


