version 1.0

workflow QcSummaryWorkflow {
    input {
        String sample_id
        String work_dir
        File fastp_log
        File alignment_metrics
        File alignment_metrics_umi_deduped
        File on_target_reads
        File on_target_reads_umi_deduped
        File hs_metrics_sorted
        File hs_metrics_umi_deduped
        File per_base_coverage_umi_deduped
        File insert_size_metrics_umi_deduped
        File dna_qc
    }

    call ParseFastpLog {
        input:
            fastp_log = fastp_log
    }

    call ParseAlignmentMetrics {
        input:
            alignment_metrics = alignment_metrics,
            alignment_metrics_umi_deduped = alignment_metrics_umi_deduped
    }

    call ParseOnTargetReads {
        input:
            on_target_reads = on_target_reads,
            on_target_reads_umi_deduped = on_target_reads_umi_deduped,
            pf_reads_aligned = ParseAlignmentMetrics.pf_reads_aligned,
            pf_reads_aligned_umi_deduped = ParseAlignmentMetrics.pf_reads_aligned_umi_deduped
    }

    call ParseHsMetrics {
        input:
            hs_metrics_sorted = hs_metrics_sorted,
            hs_metrics_umi_deduped = hs_metrics_umi_deduped
    }

    call ParsePerBaseCoverage {
        input:
            per_base_coverage = per_base_coverage_umi_deduped,
            median_coverage = ParseHsMetrics.median_coverage_after_umi_dedup,
            target_territory = ParseHsMetrics.target_territory
    }

    call ParseInsertSizeMetrics {
        input:
            insert_size_metrics = insert_size_metrics_umi_deduped
    }

    call ParseDnaQc {
        input:
            dna_qc = dna_qc,
            sample_id = sample_id
    }

    call GenerateQcSummary {
        input:
            sample_id = sample_id,
            work_dir = work_dir,
            # FastP metrics
            total_reads_pair = ParseFastpLog.total_reads_pair,
            raw_data_size = ParseFastpLog.raw_data_size,
            clean_data_size = ParseFastpLog.clean_data_size,
            read1_q20_after_filtering = ParseFastpLog.read1_q20_after_filtering,
            read2_q20_after_filtering = ParseFastpLog.read2_q20_after_filtering,
            read1_q30_after_filtering = ParseFastpLog.read1_q30_after_filtering,
            read2_q30_after_filtering = ParseFastpLog.read2_q30_after_filtering,
            # Alignment metrics
            pct_pf_reads_aligned = ParseAlignmentMetrics.pct_pf_reads_aligned,
            umi_total_duplicate_rate = ParseAlignmentMetrics.umi_total_duplicate_rate,
            # On-target metrics
            reads_on_target_rate = ParseOnTargetReads.reads_on_target_rate,
            reads_on_target_rate_after_umi_dedup = ParseOnTargetReads.reads_on_target_rate_after_umi_dedup,
            # HS metrics
            mean_coverage = ParseHsMetrics.mean_coverage,
            median_coverage = ParseHsMetrics.median_coverage,
            mean_coverage_after_umi_dedup = ParseHsMetrics.mean_coverage_after_umi_dedup,
            median_coverage_after_umi_dedup = ParseHsMetrics.median_coverage_after_umi_dedup,
            fold80_base_penalty = ParseHsMetrics.fold80_base_penalty,
            coverage_of_target_region_1X = ParseHsMetrics.coverage_of_target_region_1X,
            coverage_of_target_region_1000X = ParseHsMetrics.coverage_of_target_region_1000X,
            coverage_of_target_region_2500X = ParseHsMetrics.coverage_of_target_region_2500X,
            coverage_of_target_region_5000X = ParseHsMetrics.coverage_of_target_region_5000X,
            # Coverage statistics
            percent_bases_in_2_fold_range = ParsePerBaseCoverage.percent_bases_in_2_fold_range,
            percent_bases_point1_fold_of_unique_depth = ParsePerBaseCoverage.percent_bases_point1_fold_of_unique_depth,
            percent_bases_point2_fold_of_unique_depth = ParsePerBaseCoverage.percent_bases_point2_fold_of_unique_depth,
            percent_bases_point5_fold_of_unique_depth = ParsePerBaseCoverage.percent_bases_point5_fold_of_unique_depth,
            # Insert size metrics
            median_insert_size = ParseInsertSizeMetrics.median_insert_size,
            mean_insert_size = ParseInsertSizeMetrics.mean_insert_size,
            # DNA QC
            mass = ParseDnaQc.mass
    }

    output {
        File qc_summary_txt = GenerateQcSummary.qc_summary_txt
        File qc_summary_tsv = GenerateQcSummary.qc_summary_tsv
        File qc_csv = GenerateQcSummary.qc_csv
    }
}

task ParseFastpLog {
    input {
        File fastp_log
    }

    command <<<
        python3 <<CODE
import re

# Initialize variables
total_reads_pair = 0
read1_bases_before_filtering = 0
read2_bases_before_filtering = 0
read1_bases_after_filtering = 0
read2_bases_after_filtering = 0
read1_q30_after_filtering = 0
read2_q30_after_filtering = 0
read1_q20_after_filtering = 0
read2_q20_after_filtering = 0

# Read the fastp log file
with open("~{fastp_log}", 'r') as f:
    content = f.read()

# Split by double newlines like Perl's $/="\n\n"
blocks = content.split('\n\n')

for block in blocks:
    # Read1 before filtering
    match = re.search(r'Read1 before filtering:\ntotal reads: (\d+)\ntotal bases: (\d+)', block)
    if match:
        total_reads_pair = round(int(match.group(1)) / 1000000, 2)
        read1_bases_before_filtering = int(match.group(2))

    # Read2 before filtering
    match = re.search(r'Read2 before filtering:\ntotal reads: (\d+)\ntotal bases: (\d+)', block)
    if match:
        read2_bases_before_filtering = int(match.group(2))

    # Read1 after filtering
    match = re.search(r'Read1 after filtering:\ntotal reads: (\d+)\ntotal bases: (\d+)\nQ20 bases: (\d+)\((\d+\.\d+)%\)\nQ30 bases: (\d+)\((\d+\.\d+)%\)', block)
    if match:
        read1_bases_after_filtering = int(match.group(2))
        read1_q20_after_filtering = round(float(match.group(4)), 2)
        read1_q30_after_filtering = round(float(match.group(6)), 2)

    # Read2 after filtering
    match = re.search(r'Read2 after filtering:\ntotal reads: (\d+)\ntotal bases: (\d+)\nQ20 bases: (\d+)\((\d+\.\d+)%\)\nQ30 bases: (\d+)\((\d+\.\d+)%\)', block)
    if match:
        read2_bases_after_filtering = int(match.group(2))
        read2_q20_after_filtering = round(float(match.group(4)), 2)
        read2_q30_after_filtering = round(float(match.group(6)), 2)

raw_data_size = read1_bases_before_filtering + read2_bases_before_filtering
clean_data_size = read1_bases_after_filtering + read2_bases_after_filtering

# Write outputs to separate files
with open("total_reads_pair.txt", 'w') as f:
    f.write(str(total_reads_pair))
with open("raw_data_size.txt", 'w') as f:
    f.write(str(raw_data_size))
with open("clean_data_size.txt", 'w') as f:
    f.write(str(clean_data_size))
with open("read1_q20_after_filtering.txt", 'w') as f:
    f.write(str(read1_q20_after_filtering))
with open("read2_q20_after_filtering.txt", 'w') as f:
    f.write(str(read2_q20_after_filtering))
with open("read1_q30_after_filtering.txt", 'w') as f:
    f.write(str(read1_q30_after_filtering))
with open("read2_q30_after_filtering.txt", 'w') as f:
    f.write(str(read2_q30_after_filtering))

CODE
    >>>

    output {
        Float total_reads_pair = read_float("total_reads_pair.txt")
        Int raw_data_size = read_int("raw_data_size.txt")
        Int clean_data_size = read_int("clean_data_size.txt")
        Float read1_q20_after_filtering = read_float("read1_q20_after_filtering.txt")
        Float read2_q20_after_filtering = read_float("read2_q20_after_filtering.txt")
        Float read1_q30_after_filtering = read_float("read1_q30_after_filtering.txt")
        Float read2_q30_after_filtering = read_float("read2_q30_after_filtering.txt")
    }

    runtime {
        docker: "python:3.9-slim"
        memory: "1 GB"
        cpu: 1
    }
}

task ParseAlignmentMetrics {
    input {
        File alignment_metrics
        File alignment_metrics_umi_deduped
    }

    command <<<
        python3 <<CODE
# Parse alignment metrics
pf_reads_aligned = 0
pct_pf_reads_aligned = 0

with open("~{alignment_metrics}", 'r') as f:
    for line in f:
        line = line.strip()
        if line.startswith('PAIR'):
            fields = line.split('\t')
            pf_reads_aligned = int(fields[5])
            pct_pf_reads_aligned = round(float(fields[6]) * 100, 2)
            break

# Parse UMI deduplicated alignment metrics
pf_reads_aligned_umi_deduped = 0

with open("~{alignment_metrics_umi_deduped}", 'r') as f:
    for line in f:
        line = line.strip()
        if line.startswith('PAIR'):
            fields = line.split('\t')
            pf_reads_aligned_umi_deduped = int(fields[5])
            break

# Calculate UMI duplicate rate
umi_total_duplicate_rate = round((pf_reads_aligned - pf_reads_aligned_umi_deduped) * 100 / pf_reads_aligned, 2)

# Write outputs to separate files
with open("pf_reads_aligned.txt", 'w') as f:
    f.write(str(pf_reads_aligned))
with open("pct_pf_reads_aligned.txt", 'w') as f:
    f.write(str(pct_pf_reads_aligned))
with open("pf_reads_aligned_umi_deduped.txt", 'w') as f:
    f.write(str(pf_reads_aligned_umi_deduped))
with open("umi_total_duplicate_rate.txt", 'w') as f:
    f.write(str(umi_total_duplicate_rate))

CODE
    >>>

    output {
        Int pf_reads_aligned = read_int("pf_reads_aligned.txt")
        Float pct_pf_reads_aligned = read_float("pct_pf_reads_aligned.txt")
        Int pf_reads_aligned_umi_deduped = read_int("pf_reads_aligned_umi_deduped.txt")
        Float umi_total_duplicate_rate = read_float("umi_total_duplicate_rate.txt")
    }

    runtime {
        docker: "python:3.9-slim"
        memory: "1 GB"
        cpu: 1
    }
}

task ParseOnTargetReads {
    input {
        File on_target_reads
        File on_target_reads_umi_deduped
        Int pf_reads_aligned
        Int pf_reads_aligned_umi_deduped
    }

    command <<<
        python3 <<CODE
import re

# Parse on-target reads
on_target_reads = 0
with open("~{on_target_reads}", 'r') as f:
    for line in f:
        line = line.strip()
        match = re.match(r'^(\d+)$', line)
        if match:
            on_target_reads = int(match.group(1))
            break

# Parse on-target reads after UMI deduplication
on_target_reads_after_umi_dedup = 0
with open("~{on_target_reads_umi_deduped}", 'r') as f:
    for line in f:
        line = line.strip()
        match = re.match(r'^(\d+)$', line)
        if match:
            on_target_reads_after_umi_dedup = round(int(match.group(1)) * 100, 2)
            break

# Calculate rates
pf_reads_aligned = ~{pf_reads_aligned}
pf_reads_aligned_umi_deduped = ~{pf_reads_aligned_umi_deduped}

reads_on_target_rate = round((on_target_reads / pf_reads_aligned) * 100, 2)
reads_on_target_rate_after_umi_dedup = round((on_target_reads_after_umi_dedup / pf_reads_aligned_umi_deduped), 2)

# Write outputs to separate files
with open("reads_on_target_rate.txt", 'w') as f:
    f.write(str(reads_on_target_rate))
with open("reads_on_target_rate_after_umi_dedup.txt", 'w') as f:
    f.write(str(reads_on_target_rate_after_umi_dedup))

CODE
    >>>

    output {
        Float reads_on_target_rate = read_float("reads_on_target_rate.txt")
        Float reads_on_target_rate_after_umi_dedup = read_float("reads_on_target_rate_after_umi_dedup.txt")
    }

    runtime {
        docker: "python:3.9-slim"
        memory: "1 GB"
        cpu: 1
    }
}

task ParseHsMetrics {
    input {
        File hs_metrics_sorted
        File hs_metrics_umi_deduped
    }

    command <<<
        python3 <<CODE
# Parse HS metrics sorted
mean_coverage = 0
median_coverage = 0

with open("~{hs_metrics_sorted}", 'r') as f:
    for line in f:
        if line.startswith('DESIGN'):
            fields = line.strip().split('\t')
            mean_coverage = round(float(fields[33]), 2)
            median_coverage = round(float(fields[34]), 2)
            break

# Parse HS metrics UMI deduplicated
fold80_base_penalty = 0
mean_coverage_after_umi_dedup = 0
median_coverage_after_umi_dedup = 0
target_territory = 0
coverage_of_target_region_1X = 0
coverage_of_target_region_1000X = 0
coverage_of_target_region_2500X = 0
coverage_of_target_region_5000X = 0

with open("~{hs_metrics_umi_deduped}", 'r') as f:
    for line in f:
        if line.startswith('DESIGN'):
            fields = line.strip().split('\t')
            fold80_base_penalty = round(float(fields[44]), 2)
            mean_coverage_after_umi_dedup = round(float(fields[33]), 2)
            median_coverage_after_umi_dedup = round(float(fields[34]), 2)
            target_territory = round(float(fields[20]), 2)
            coverage_of_target_region_1X = round(float(fields[45]) * 100, 2)
            coverage_of_target_region_1000X = round(float(fields[55]) * 100, 2)
            coverage_of_target_region_2500X = round(float(fields[56]) * 100, 2)
            coverage_of_target_region_5000X = round(float(fields[57]) * 100, 2)
            break

# Write outputs to separate files
with open("mean_coverage.txt", 'w') as f:
    f.write(str(mean_coverage))
with open("median_coverage.txt", 'w') as f:
    f.write(str(median_coverage))
with open("fold80_base_penalty.txt", 'w') as f:
    f.write(str(fold80_base_penalty))
with open("mean_coverage_after_umi_dedup.txt", 'w') as f:
    f.write(str(mean_coverage_after_umi_dedup))
with open("median_coverage_after_umi_dedup.txt", 'w') as f:
    f.write(str(median_coverage_after_umi_dedup))
with open("target_territory.txt", 'w') as f:
    f.write(str(target_territory))
with open("coverage_of_target_region_1X.txt", 'w') as f:
    f.write(str(coverage_of_target_region_1X))
with open("coverage_of_target_region_1000X.txt", 'w') as f:
    f.write(str(coverage_of_target_region_1000X))
with open("coverage_of_target_region_2500X.txt", 'w') as f:
    f.write(str(coverage_of_target_region_2500X))
with open("coverage_of_target_region_5000X.txt", 'w') as f:
    f.write(str(coverage_of_target_region_5000X))

CODE
    >>>

    output {
        Float mean_coverage = read_float("mean_coverage.txt")
        Float median_coverage = read_float("median_coverage.txt")
        Float fold80_base_penalty = read_float("fold80_base_penalty.txt")
        Float mean_coverage_after_umi_dedup = read_float("mean_coverage_after_umi_dedup.txt")
        Float median_coverage_after_umi_dedup = read_float("median_coverage_after_umi_dedup.txt")
        Float target_territory = read_float("target_territory.txt")
        Float coverage_of_target_region_1X = read_float("coverage_of_target_region_1X.txt")
        Float coverage_of_target_region_1000X = read_float("coverage_of_target_region_1000X.txt")
        Float coverage_of_target_region_2500X = read_float("coverage_of_target_region_2500X.txt")
        Float coverage_of_target_region_5000X = read_float("coverage_of_target_region_5000X.txt")
    }

    runtime {
        docker: "python:3.9-slim"
        memory: "1 GB"
        cpu: 1
    }
}

task ParsePerBaseCoverage {
    input {
        File per_base_coverage
        Float median_coverage
        Float target_territory
    }

    command <<<
        python3 <<CODE
median_coverage = ~{median_coverage}
target_territory = ~{target_territory}

fold2_range_bases = 0
point1_coverage_bases = 0
point2_coverage_bases = 0
point5_coverage_bases = 0

with open("~{per_base_coverage}", 'r') as f:
    for line in f:
        line = line.strip()
        if line.startswith('chrom'):
            continue  # Skip header

        fields = line.split('\t')
        if len(fields) >= 4:
            coverage = float(fields[3])

            # Check fold2 range (0.5x to 2x median coverage)
            if coverage >= median_coverage / 2 and coverage <= median_coverage * 2:
                fold2_range_bases += 1

            # Check 0.1x coverage
            if coverage >= 0.1 * median_coverage:
                point1_coverage_bases += 1

            # Check 0.2x coverage
            if coverage >= 0.2 * median_coverage:
                point2_coverage_bases += 1

            # Check 0.5x coverage
            if coverage >= 0.5 * median_coverage:
                point5_coverage_bases += 1

# Calculate percentages
percent_bases_in_2_fold_range = round((fold2_range_bases / target_territory) * 100, 2)
percent_bases_point1_fold_of_unique_depth = round((point1_coverage_bases / target_territory) * 100, 2)
percent_bases_point2_fold_of_unique_depth = round((point2_coverage_bases / target_territory) * 100, 2)
percent_bases_point5_fold_of_unique_depth = round((point5_coverage_bases / target_territory) * 100, 2)

# Write outputs to separate files
with open("percent_bases_in_2_fold_range.txt", 'w') as f:
    f.write(str(percent_bases_in_2_fold_range))
with open("percent_bases_point1_fold_of_unique_depth.txt", 'w') as f:
    f.write(str(percent_bases_point1_fold_of_unique_depth))
with open("percent_bases_point2_fold_of_unique_depth.txt", 'w') as f:
    f.write(str(percent_bases_point2_fold_of_unique_depth))
with open("percent_bases_point5_fold_of_unique_depth.txt", 'w') as f:
    f.write(str(percent_bases_point5_fold_of_unique_depth))

CODE
    >>>

    output {
        Float percent_bases_in_2_fold_range = read_float("percent_bases_in_2_fold_range.txt")
        Float percent_bases_point1_fold_of_unique_depth = read_float("percent_bases_point1_fold_of_unique_depth.txt")
        Float percent_bases_point2_fold_of_unique_depth = read_float("percent_bases_point2_fold_of_unique_depth.txt")
        Float percent_bases_point5_fold_of_unique_depth = read_float("percent_bases_point5_fold_of_unique_depth.txt")
    }

    runtime {
        docker: "python:3.9-slim"
        memory: "2 GB"
        cpu: 1
    }
}

task ParseInsertSizeMetrics {
    input {
        File insert_size_metrics
    }

    command <<<
        python3 <<CODE
median_insert_size = 0
mean_insert_size = 0

with open("~{insert_size_metrics}", 'r') as f:
    lines = f.readlines()
    for i, line in enumerate(lines):
        line = line.strip()
        if line.startswith('MEDIAN_INSERT_SIZE'):
            # Next line contains the data
            if i + 1 < len(lines):
                data_line = lines[i + 1].strip()
                fields = data_line.split('\t')
                median_insert_size = int(fields[0])
                mean_insert_size = round(float(fields[5]), 2)
            break

# Write outputs to separate files
with open("median_insert_size.txt", 'w') as f:
    f.write(str(median_insert_size))
with open("mean_insert_size.txt", 'w') as f:
    f.write(str(mean_insert_size))

CODE
    >>>

    output {
        Int median_insert_size = read_int("median_insert_size.txt")
        Float mean_insert_size = read_float("mean_insert_size.txt")
    }

    runtime {
        docker: "python:3.9-slim"
        memory: "1 GB"
        cpu: 1
    }
}

task ParseDnaQc {
    input {
        File dna_qc
        String sample_id
    }

    command <<<
        python3 <<CODE
import re

mass = 0.0
sample_id = "~{sample_id}"

with open("~{dna_qc}", 'r') as f:
    for line in f:
        line = line.strip()
        if line.startswith('Sample_ID'):
            continue  # Skip header

        # Match pattern: sample_id whitespace total_dna whitespace plasma_vol
        match = re.match(r'^(\S+)\s+(\S+)\s+(\S+)$', line)
        if match:
            file_sample_id, total_dna, plasma_vol = match.groups()
            if file_sample_id == sample_id:
                mass = float(total_dna)
                break

# Write outputs to separate files
with open("mass.txt", 'w') as f:
    f.write(str(mass))

CODE
    >>>

    output {
        Float mass = read_float("mass.txt")
    }

    runtime {
        docker: "python:3.9-slim"
        memory: "1 GB"
        cpu: 1
    }
}

task GenerateQcSummary {
    input {
        String sample_id
        String work_dir
        # FastP metrics
        Float total_reads_pair
        Int raw_data_size
        Int clean_data_size
        Float read1_q20_after_filtering
        Float read2_q20_after_filtering
        Float read1_q30_after_filtering
        Float read2_q30_after_filtering
        # Alignment metrics
        Float pct_pf_reads_aligned
        Float umi_total_duplicate_rate
        # On-target metrics
        Float reads_on_target_rate
        Float reads_on_target_rate_after_umi_dedup
        # HS metrics
        Float mean_coverage
        Float median_coverage
        Float mean_coverage_after_umi_dedup
        Float median_coverage_after_umi_dedup
        Float fold80_base_penalty
        Float coverage_of_target_region_1X
        Float coverage_of_target_region_1000X
        Float coverage_of_target_region_2500X
        Float coverage_of_target_region_5000X
        # Coverage statistics
        Float percent_bases_in_2_fold_range
        Float percent_bases_point1_fold_of_unique_depth
        Float percent_bases_point2_fold_of_unique_depth
        Float percent_bases_point5_fold_of_unique_depth
        # Insert size metrics
        Int median_insert_size
        Float mean_insert_size
        # DNA QC
        Float mass
    }

    command <<<
        python3 <<CODE
import re

sample_id = "~{sample_id}"

# Generate QC summary text file
with open(f"{sample_id}_qc_summary.txt", 'w') as f:
    f.write(f"""Sample_ID:{sample_id}
Raw_data_size:~{raw_data_size}
Total_reads(M pairs):~{total_reads_pair}
Q20_R1(%):~{read1_q20_after_filtering}
Q20_R2(%):~{read2_q20_after_filtering}
Q30_R1(%):~{read1_q30_after_filtering}
Q30_R2(%):~{read2_q30_after_filtering}
Median_insert_size(bp):~{median_insert_size}
Mean_insert_size(bp):~{mean_insert_size}
Duplication_rate(%):~{umi_total_duplicate_rate}
Mapping_rate(%):~{pct_pf_reads_aligned}
On_target_rate(%):~{reads_on_target_rate}
On_target_rate_umi_dedup(%):~{reads_on_target_rate_after_umi_dedup}
Coverage_of_target_region(%):~{coverage_of_target_region_1X}
Coverage_of_target_region_1000X(%):~{coverage_of_target_region_1000X}
Coverage_of_target_region_2500X(%):~{coverage_of_target_region_2500X}
Coverage_of_target_region_5000X(%):~{coverage_of_target_region_5000X}
Mean_depth:~{mean_coverage}
Mean_depth_umi_dedup:~{mean_coverage_after_umi_dedup}
Fold_80_base_penalty:~{fold80_base_penalty}
Bases_in_2fold_range(%):~{percent_bases_in_2_fold_range}
Bases_0.1x_unique_depth(%):~{percent_bases_point1_fold_of_unique_depth}
Bases_0.2x_unique_depth(%):~{percent_bases_point2_fold_of_unique_depth}
Bases_0.5x_unique_depth(%):~{percent_bases_point5_fold_of_unique_depth}
""")

# Generate QC summary TSV file
with open(f"{sample_id}_qc_summary.tsv", 'w') as f:
    # Header
    f.write("Sample_ID\tRaw_data_size\tTotal_reads(M pairs)\tQ20_R1(%)\tQ20_R2(%)\tQ30_R1(%)\tQ30_R2(%)\tMedian_insert_size(bp)\tMean_insert_size(bp)\tDuplication_rate(%)\tMapping_rate(%)\tOn_target_rate(%)\tOn_target_rate_umi_dedup(%)\tCoverage_of_target_region(%)\tCoverage_of_target_region_1000X(%)\tCoverage_of_target_region_2500X(%)\tCoverage_of_target_region_5000X(%)\tMean_depth\tMean_depth_umi_dedup\tFold_80_base_penalty\tBases_in_2fold_range(%)\tBases_0.1x_unique_depth(%)\tBases_0.2x_unique_depth(%)\tBases_0.5x_unique_depth(%)\n")
    # Data
    f.write(f"{sample_id}\t~{raw_data_size}\t~{total_reads_pair}\t~{read1_q20_after_filtering}\t~{read2_q20_after_filtering}\t~{read1_q30_after_filtering}\t~{read2_q30_after_filtering}\t~{median_insert_size}\t~{mean_insert_size}\t~{umi_total_duplicate_rate}\t~{pct_pf_reads_aligned}\t~{reads_on_target_rate}\t~{reads_on_target_rate_after_umi_dedup}\t~{coverage_of_target_region_1X}\t~{coverage_of_target_region_1000X}\t~{coverage_of_target_region_2500X}\t~{coverage_of_target_region_5000X}\t~{mean_coverage}\t~{mean_coverage_after_umi_dedup}\t~{fold80_base_penalty}\t~{percent_bases_in_2_fold_range}\t~{percent_bases_point1_fold_of_unique_depth}\t~{percent_bases_point2_fold_of_unique_depth}\t~{percent_bases_point5_fold_of_unique_depth}\n")

# Generate QC CSV file (Chinese format)
# Determine sample type
sample_type = "ctDNA样本"
if re.match(r'^.{9}B', sample_id):
    sample_type = "gDNA样本"

with open(f"{sample_id}_qc.csv", 'w') as f:
    f.write(f"""质量参数,,{sample_type}
DNA质量评估,送检DNA总量,~{mass}ng
测序质量评估,测序数据量,~{total_reads_pair}M
,插入片段长度,~{median_insert_size}bp
,序列回贴比率,~{pct_pf_reads_aligned}%
,中靶率,~{reads_on_target_rate}%
,平均测序深度,~{mean_coverage}x
,测序均一性,~{coverage_of_target_region_1000X}%
,总体质量评估,合格
""")

CODE
    >>>

    output {
        File qc_summary_txt = "${sample_id}_qc_summary.txt"
        File qc_summary_tsv = "${sample_id}_qc_summary.tsv"
        File qc_csv = "${sample_id}_qc.csv"
    }

    runtime {
        docker: "python:3.9-slim"
        memory: "1 GB"
        cpu: 1
    }
}


