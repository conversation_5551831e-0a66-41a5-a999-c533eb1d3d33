#!/usr/bin/perl
use strict;
use warnings;

my $sample="LC2509641B01";
my $dir="/Users/<USER>/workspace/gene-analysis/scripts/LCMRD/";
$/="\n\n";
my $total_reads_pair=0;
my $read1_bases_before_filtering=0;
my $read2_bases_before_filtering=0;
my $read1_bases_after_filtering=0;
my $read2_bases_after_filtering=0;
my $read1_q30_after_filtering=0;
my $read2_q30_after_filtering=0;
my $read1_q20_after_filtering=0;
my $read2_q20_after_filtering=0;

open FPIN,"$$dir/fastq/${sample}_fastp.log" or die $!;
while(<FPIN>){
	if(/Read1 before filtering:\ntotal reads: (\d+)\ntotal bases: (\d+)/){
                $total_reads_pair=sprintf("%.2f", $1/1000000);
		$read1_bases_before_filtering=$2;
	}
	if(/Read2 before filtering:\ntotal reads: (\d+)\ntotal bases: (\d+)/){
		$read2_bases_before_filtering=$2;
	}
	if(/Read1 after filtering:\ntotal reads: (\d+)\ntotal bases: (\d+)\nQ20 bases: (\d+)\((\d+\.\d+)%\)\nQ30 bases: (\d+)\((\d+\.\d+)%\)/){
		$read1_bases_after_filtering=$2;
                $read1_q30_after_filtering=sprintf("%0.2f",$6);
                $read1_q20_after_filtering=sprintf("%0.2f",$4);
	}
	if(/Read2 after filtering:\ntotal reads: (\d+)\ntotal bases: (\d+)\nQ20 bases: (\d+)\((\d+\.\d+)%\)\nQ30 bases: (\d+)\((\d+\.\d+)%\)/){
		$read2_bases_after_filtering=$2;
                $read2_q30_after_filtering=sprintf("%0.2f",$6);
                $read2_q20_after_filtering=sprintf("%0.2f",$4);
	}
        
}
my $raw_data_size=$read1_bases_before_filtering+$read2_bases_before_filtering;
my $clean_data_size=$read1_bases_after_filtering+$read2_bases_after_filtering;
close FPIN;
$/="\n";

# -----------------------------------------------
#Percentage of aligned reads identified as PCR duplicates
my $PF_READS_ALIGNED=0;
my $PCT_PF_READS_ALIGNED=0;
open AMIN,"$dir/QC/${sample}_alignment_metrics.txt" or die $!;
while(<AMIN>){
        chomp;
        if(/^PAIR/){
                $PF_READS_ALIGNED=(split /\t/,$_)[5];
                $PCT_PF_READS_ALIGNED=sprintf("%0.2f",(split /\t/,$_)[6]*100);
        }
}
close AMIN;

my $PF_READS_ALIGNED_umi_deduped=0;
open IN,"$dir//QC/${sample}_alignment_metrics_umi_deduped.txt" or die $!;
while(<IN>){
        chomp;
        if(/^PAIR/){
                $PF_READS_ALIGNED_umi_deduped=(split /\t/,$_)[5];
        }
}
close IN;

my $umi_total_duplicate_rate=sprintf("%0.2f",($PF_READS_ALIGNED-$PF_READS_ALIGNED_umi_deduped)*100/$PF_READS_ALIGNED);
# -----------------------------------------------

# -----------------------------------------------
#Percentage of mapped reads overlapping a target region by at least 1 base. No padding/buffering.
my $on_target_reads=0;
open OTIN, "$dir/QC/${sample}_on_target_reads.txt" or die $!;
while(<OTIN>){
	chomp;
	if(/^(\d+)$/){
		$on_target_reads=$1;
	}
}
close OTIN;

my $on_target_reads_after_umi_dedup=0;
open OTRIN, "$dir//QC/${sample}_on_target_reads_umi_deduped.txt" or die $!;
while(<OTRIN>){
        chomp;
        if(/^(\d+)$/){
                $on_target_reads_after_umi_dedup=sprintf("%0.2f",$1*100);
        }
}
close OTRIN;
my $reads_on_target_rate=sprintf("%0.2f",($on_target_reads/$PF_READS_ALIGNED)*100);
my $reads_on_target_rate_after_umi_dedup=sprintf("%0.2f",($on_target_reads_after_umi_dedup/$PF_READS_ALIGNED_umi_deduped));
# -----------------------------------------------

# -----------------------------------------------
my $mean_coverage=0;
my $median_coverage=0;
open HSIN,"$dir//QC/${sample}_hs_metrics_sorted.txt" or die $!;
while(<HSIN>){
        if(/^DESIGN/){
                $mean_coverage=sprintf("%0.2f",(split /\t/,$_)[33]);
		$median_coverage=sprintf("%0.2f",(split /\t/,$_)[34]);
        }
}
close HSIN;

my $fold80_base_penalty=0;
my $mean_coverage_after_umi_dedup=0;
my $median_coverage_after_umi_dedup=0;
my $target_territory=0;
my $coverage_of_target_region_1X=0;
my $coverage_of_target_region_1000X=0;
my $coverage_of_target_region_2500X=0;
my $coverage_of_target_region_5000X=0;
#PCT_TARGET_BASES_1X     PCT_TARGET_BASES_2X     PCT_TARGET_BASES_10X    PCT_TARGET_BASES_20X    PCT_TARGET_BASES_30X    PCT_TARGET_BASES_40X    PCT_TARGET_BASES_50X    PCT_TARGET_BASES_100X   PCT_TARGET_BASES_250X   PCT_TARGET_BASES_500X   PCT_TARGET_BASES_1000X  PCT_TARGET_BASES_2500X  PCT_TARGET_BASES_5000X  PCT_TARGET_BASES_10000X PCT_TARGET_BASES_25000X PCT_TARGET_BASES_50000X
open HSIN,"$dir//QC/${sample}_hs_metrics_umi_deduped.txt" or die $!;
while(<HSIN>){
	if(/^DESIGN/){
		$fold80_base_penalty=sprintf("%0.2f",(split /\t/,$_)[44]);
		$mean_coverage_after_umi_dedup=sprintf("%0.2f",(split /\t/,$_)[33]);
		$median_coverage_after_umi_dedup=sprintf("%0.2f",(split /\t/,$_)[34]);
		$target_territory=sprintf("%0.2f",(split /\t/,$_)[20]);
                $coverage_of_target_region_1X=sprintf("%0.2f",((split /\t/,$_)[45])*100);
                $coverage_of_target_region_1000X=sprintf("%0.2f",((split /\t/,$_)[55])*100);
                $coverage_of_target_region_2500X=sprintf("%0.2f",((split /\t/,$_)[56])*100);
                $coverage_of_target_region_5000X=sprintf("%0.2f",((split /\t/,$_)[57])*100);
	}
}
close HSIN;

my $fold2_range_bases=0;
my $point1_coverage_bases=0;
my $point2_coverage_bases=0;
my $point5_coverage_bases=0;
open PBCUD,"$dir//QC/${sample}_per_base_coverage_umi_deduped.txt" or die $!;
while(<PBCUD>){
	next if /^chrom/;
	my $coverage=(split /\t/,$_)[3];
	if($coverage >= $median_coverage_after_umi_dedup/2 && $coverage <= $median_coverage_after_umi_dedup*2){
		$fold2_range_bases++;
	}
	if($coverage >= 0.1*$median_coverage_after_umi_dedup){
		$point1_coverage_bases++;
	}
	if($coverage >= 0.2*$median_coverage_after_umi_dedup){
		$point2_coverage_bases++;
	}
	if($coverage >= 0.5*$median_coverage_after_umi_dedup){
		$point5_coverage_bases++;
	}
}

my $percent_bases_in_2_fold_range=sprintf("%0.2f",($fold2_range_bases/$target_territory)*100);
my $percent_bases_point1_fold_of_unique_depth=sprintf("%0.2f",($point1_coverage_bases/$target_territory)*100);
my $percent_bases_point2_fold_of_unique_depth=sprintf("%0.2f",($point2_coverage_bases/$target_territory)*100);
my $percent_bases_point5_fold_of_unique_depth=sprintf("%0.2f",($point5_coverage_bases/$target_territory)*100);
# -----------------------------------------------

# -----------------------------------------------
my $median_insert_size=0;
my $mean_insert_size=0;
open IIN,"$dir//QC/${sample}_insert_size_metrics_umi_deduped.txt" or die $!;
while(<IIN>){
	chomp;
	if(/^MEDIAN_INSERT_SIZE/){
		my $line=<IIN>;
		my @arr=split/\t/,$line;
		$median_insert_size=$arr[0];
		$mean_insert_size=sprintf("%0.2f",$arr[5]);
	}
}
close IIN;
# -----------------------------------------------

# -----------------------------------------------
my $mass=0.0;
my $vol=4;
#open QIN,"$dir//QC/${sample}_dna_qc.txt" or die $!;
open QIN,"$dir/dna_qc.txt" or die $!;
while(my $line=<QIN>){
        #chomp;
        #if(/^$sample\t(\d+)\t(\d+)/){
        #       $mass=$1;
        #       $vol=$2;
        #}
        chomp $line;
        next if $line =~ /^Sample_ID/;  # 跳过表头行

        if ($line =~ /^(\S+)\s+(\S+)\s+(\S+)$/) {
                my ($sample_id, $total_dna, $plasma_vol) = ($1, $2, $3);
        
                if ($sample_id eq $sample) {
                        print $sample;
                        $mass=$total_dna;
                        #$vol=$plasma_vol;
                }
        }
}
close QIN;
# -----------------------------------------------

# -----------------------------------------------
open OUT,">$dir//QC/${sample}_qc_summary.txt" or die $!;
print OUT <<EOF;
Sample_ID:$sample
Raw_data_size:$raw_data_size
Total_reads(M pairs):$total_reads_pair
Q20_R1(%):$read1_q20_after_filtering
Q20_R2(%):$read2_q20_after_filtering
Q30_R1(%):$read1_q30_after_filtering
Q30_R2(%):$read2_q30_after_filtering
Median_insert_size(bp):$median_insert_size
Mean_insert_size(bp):$mean_insert_size
Duplication_rate(%):$umi_total_duplicate_rate
Mapping_rate(%):$PCT_PF_READS_ALIGNED
On_target_rate(%):$reads_on_target_rate
On_target_rate_umi_dedup(%):$reads_on_target_rate_after_umi_dedup
Coverage_of_target_region(%):$coverage_of_target_region_1X
Coverage_of_target_region_1000X(%):$coverage_of_target_region_1000X
Coverage_of_target_region_2500X(%):$coverage_of_target_region_2500X
Coverage_of_target_region_5000X(%):$coverage_of_target_region_5000X
Mean_depth:$mean_coverage
Mean_depth_umi_dedup:$mean_coverage_after_umi_dedup
Fold_80_base_penalty:$fold80_base_penalty
Bases_in_2fold_range(%):$percent_bases_in_2_fold_range
Bases_0.1x_unique_depth(%):$percent_bases_point1_fold_of_unique_depth
Bases_0.2x_unique_depth(%):$percent_bases_point2_fold_of_unique_depth
Bases_0.5x_unique_depth(%):$percent_bases_point5_fold_of_unique_depth
EOF
# -----------------------------------------------


# -----------------------------------------------
#Sample_ID\tTotalReads\tMean_depth\tMean_depth_umi_dedup\tOn_target_rate(%)\tDuplication_rate(%)\tMedian_insert_size(bp)\tMapping_rate(%)\tCoverage_of_target_region(%)\tFraction_of_target_covered_with_at_least_0.2X_Mean_depth\tQ20_R1(%)\tQ20_R2(%)\tQ30_R1(%)\tQ30_R2(%)
##$sample\t$total_reads_pair\t$mean_coverage\t$mean_coverage_after_umi_dedup\t$reads_on_target_rate\t$umi_total_duplicate_rate\t$median_insert_size\t$PCT_PF_READS_ALIGNED\t$coverage_of_target_region_1X\t$percent_bases_point2_fold_of_unique_depth\t$read1_q20_after_filtering\t$read2_q20_after_filtering\t$read1_q30_after_filtering\t$read2_q30_after_filtering
open OUT1,">$dir//QC/${sample}_qc_summary.tsv" or die $!;
print OUT1 <<EOF;
Sample_ID\tRaw_data_size\tTotal_reads(M pairs)\tQ20_R1(%)\tQ20_R2(%)\tQ30_R1(%)\tQ30_R2(%)\tMedian_insert_size(bp)\tMean_insert_size(bp)\tDuplication_rate(%)\tMapping_rate(%)\tOn_target_rate(%)\tOn_target_rate_umi_dedup(%)\tCoverage_of_target_region(%)\tCoverage_of_target_region_1000X(%)\tCoverage_of_target_region_2500X(%)\tCoverage_of_target_region_5000X(%)\tMean_depth\tMean_depth_umi_dedup\tFold_80_base_penalty\tBases_in_2fold_range(%)\tBases_0.1x_unique_depth(%)\tBases_0.2x_unique_depth(%)\tBases_0.5x_unique_depth(%)
$sample\t$raw_data_size\t$total_reads_pair\t$read1_q20_after_filtering\t$read2_q20_after_filtering\t$read1_q30_after_filtering\t$read2_q30_after_filtering\t$median_insert_size\t$mean_insert_size\t$umi_total_duplicate_rate\t$PCT_PF_READS_ALIGNED\t$reads_on_target_rate\t$reads_on_target_rate_after_umi_dedup\t$coverage_of_target_region_1X\t$coverage_of_target_region_1000X\t$coverage_of_target_region_2500X\t$coverage_of_target_region_5000X\t$mean_coverage\t$mean_coverage_after_umi_dedup\t$fold80_base_penalty\t$percent_bases_in_2_fold_range\t$percent_bases_point1_fold_of_unique_depth\t$percent_bases_point2_fold_of_unique_depth\t$percent_bases_point5_fold_of_unique_depth
EOF
# -----------------------------------------------

# -----------------------------------------------
my $sample_type="ctDNA样本";

if ($sample =~ /^.{9}B/){
    $sample_type = "gDNA样本";   
}
open OUT2,">$dir//QC/${sample}_qc.csv" or die $!;
print OUT2 <<EOF;
质量参数,,${sample_type}
DNA质量评估,送检DNA总量,${mass}ng
测序质量评估,测序数据量,${total_reads_pair}M
,插入片段长度,${median_insert_size}bp
,序列回贴比率,${PCT_PF_READS_ALIGNED}%
,中靶率,${reads_on_target_rate}%
,平均测序深度,${mean_coverage}x
,测序均一性,${coverage_of_target_region_1000X}%
,总体质量评估,合格
EOF
# -----------------------------------------------
