#!/bin/bash

# Script to validate the WDL workflow
# Requires miniwdl to be installed: pip install miniwdl

echo "Validating QC Summary WDL workflow..."

# Check if miniwdl is installed
if ! command -v miniwdl &> /dev/null; then
    echo "Error: miniwdl is not installed. Please install it with: pip install miniwdl"
    exit 1
fi

# Validate the WDL file
echo "Checking WDL syntax..."
miniwdl check qc_summary_workflow.wdl

if [ $? -eq 0 ]; then
    echo "✅ WDL syntax validation passed!"
else
    echo "❌ WDL syntax validation failed!"
    exit 1
fi

# Check if input file exists
if [ -f "qc_summary_inputs.json" ]; then
    echo "✅ Input JSON file found"
else
    echo "⚠️  Input JSON file not found. Please create qc_summary_inputs.json"
fi

echo "Validation complete!"
