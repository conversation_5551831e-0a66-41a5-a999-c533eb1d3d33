import pandas as pd
import sys,os

def calculate_mmpm(file_path, extraction_mass, plasma_volume):
    # 读取CSV文件
    data = pd.read_csv(file_path,sep="\t",header=0)
    # 计算MMPM
    MMPM=0
    plasma_volume = float(plasma_volume)
    mean_value = float(data['followup_af'].mean())
    extraction_mass = float(extraction_mass)

    if len(data) > 0:
        MMPM = (mean_value * extraction_mass * 330) / (plasma_volume )
    return MMPM

# 示例参数

infile_path1 = sys.argv[1]    # selected reporters file:*_selected_baseline_reporters.tsv
infile_path2 = sys.argv[2]    # *_longitudinal_evaluation.csv
outfile_path = sys.argv[3]    # *_longitudinal_evaluation_mmpm.csv
sample_id = sys.argv[4]
extraction_mass = sys.argv[5] or 0.0 # ctDNA total mass
plasma_volume = sys.argv[6] or 8.0 # total plasma

# 计算MMPM
mmpm_values = calculate_mmpm(infile_path1, extraction_mass, plasma_volume)
print(mmpm_values)

#mmpm写入到p value文件最后一列*_longitudinal_evaluation.csv
df = pd.read_csv(infile_path2,sep=",",header=0)
df['background_rate'] = df['background_rate'].apply(lambda x: round(x, 6))
df['pvalue'] = df['pvalue'].apply(lambda x: round(x, 5))
df['mmpm'] = '%.2f' % mmpm_values
df.to_csv(outfile_path, index=False)
